/* components/ui/button/index.wxss */

/**
 * 统一按钮组件样式
 * 基于设计系统的按钮规范
 */

.ui-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: 12rpx;
  font-weight: 500;
  transition: all 150ms cubic-bezier(0.16, 1, 0.3, 1);
  border: none;
  outline: none;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

/* 按钮变体 */
.ui-button.primary {
  background-color: #0a0a0a;
  color: #ffffff;
}

.ui-button.primary:not(.disabled):active {
  background-color: #737373;
  transform: translateY(1rpx);
}

.ui-button.secondary {
  background-color: #f5f5f5;
  color: #0a0a0a;
  border: 1px solid #e5e5e5;
}

.ui-button.secondary:not(.disabled):active {
  background-color: #fafafa;
  border-color: #e5e5e5;
}

.ui-button.ghost {
  background-color: transparent;
  color: #737373;
}

.ui-button.ghost:not(.disabled):active {
  background-color: #fafafa;
  color: #0a0a0a;
}

.ui-button.destructive {
  background-color: #dc2626;
  color: #ffffff;
}

.ui-button.destructive:not(.disabled):active {
  opacity: 0.9;
}

/* 按钮尺寸 */
.ui-button.sm {
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  min-height: 64rpx;
}

.ui-button.default {
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  min-height: 88rpx;
}

.ui-button.lg {
  padding: 32rpx 64rpx;
  font-size: 32rpx;
  min-height: 96rpx;
}

/* 按钮状态 */
.ui-button.disabled {
  opacity: 0.4;
  pointer-events: none;
}

.ui-button-hover {
  transform: translateY(-1rpx);
}

/* 加载状态 */
.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.button-text {
  line-height: 1;
}
