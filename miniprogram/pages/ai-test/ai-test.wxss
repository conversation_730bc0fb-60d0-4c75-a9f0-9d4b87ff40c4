/* pages/ai-test/ai-test.wxss */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  padding: 40rpx 30rpx 30rpx;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.messages {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.message-item {
  margin-bottom: 30rpx;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.sender {
  font-size: 24rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.sender.ai {
  background: rgba(103, 126, 234, 0.2);
  color: #677eea;
}

.sender.user {
  background: rgba(118, 75, 162, 0.2);
  color: #764ba2;
}

.timestamp {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
}

.message-content {
  padding: 20rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  line-height: 1.5;
  max-width: 80%;
}

.ai-message {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  margin-right: auto;
}

.user-message {
  background: rgba(118, 75, 162, 0.8);
  color: white;
  margin-left: auto;
}

.loading {
  text-align: center;
  padding: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

.suggestions {
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
}

.suggestions-title {
  display: block;
  font-size: 24rpx;
  color: white;
  margin-bottom: 15rpx;
}

.suggestion-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.suggestion-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
  margin: 0;
}

.suggestion-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

.input-area {
  display: flex;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  gap: 15rpx;
}

.input {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 30rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
}

.send-btn {
  background: rgba(118, 75, 162, 0.6);
  color: white;
  border: none;
  border-radius: 30rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  margin: 0;
}

.send-btn.active {
  background: #764ba2;
}

.send-btn:disabled {
  background: rgba(118, 75, 162, 0.3);
}
