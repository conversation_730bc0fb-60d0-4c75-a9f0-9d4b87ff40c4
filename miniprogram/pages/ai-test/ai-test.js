// pages/ai-test/ai-test.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    messages: [],
    inputValue: '',
    isLoading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.addMessage('AI', '你好！我是小简，你的专属简历顾问。让我们开始制作你的简历吧！')
  },

  /**
   * 输入框内容变化
   */
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value
    })
  },

  /**
   * 发送消息
   */
  async sendMessage() {
    const message = this.data.inputValue.trim()
    if (!message) return

    // 添加用户消息
    this.addMessage('用户', message)
    this.setData({
      inputValue: '',
      isLoading: true
    })

    try {
      // 调用AI云函数
      const result = await wx.cloud.callFunction({
        name: 'aiChat',
        data: {
          message: message,
          conversationId: 'test-conversation',
          currentStage: 'greeting',
          resumeData: {},
          history: []
        }
      })

      if (result.result.success) {
        this.addMessage('AI', result.result.aiResponse)
        
        // 显示建议
        if (result.result.suggestions && result.result.suggestions.length > 0) {
          this.setData({
            suggestions: result.result.suggestions
          })
        }
      } else {
        this.addMessage('AI', '抱歉，我遇到了一些问题，请稍后再试。')
      }
    } catch (error) {
      console.error('AI调用失败:', error)
      this.addMessage('AI', '网络连接失败，请检查网络后重试。')
    }

    this.setData({
      isLoading: false
    })
  },

  /**
   * 添加消息
   */
  addMessage(sender, content) {
    const message = {
      id: Date.now(),
      sender: sender,
      content: content,
      timestamp: new Date().toLocaleTimeString()
    }

    this.setData({
      messages: [...this.data.messages, message]
    })

    // 滚动到底部
    setTimeout(() => {
      wx.pageScrollTo({
        scrollTop: 999999,
        duration: 300
      })
    }, 100)
  },

  /**
   * 点击建议
   */
  onSuggestionTap(e) {
    const suggestion = e.currentTarget.dataset.suggestion
    this.setData({
      inputValue: suggestion
    })
  }
})
