<!--pages/ai-test/ai-test.wxml-->
<view class="container">
  <!-- 标题 -->
  <view class="header">
    <text class="title">🤖 AI简历顾问测试</text>
    <text class="subtitle">测试腾讯云开发AI能力集成</text>
  </view>

  <!-- 消息列表 -->
  <scroll-view class="messages" scroll-y="true" scroll-top="{{scrollTop}}">
    <view class="message-item" wx:for="{{messages}}" wx:key="id">
      <view class="message-header">
        <text class="sender {{item.sender === 'AI' ? 'ai' : 'user'}}">{{item.sender}}</text>
        <text class="timestamp">{{item.timestamp}}</text>
      </view>
      <view class="message-content {{item.sender === 'AI' ? 'ai-message' : 'user-message'}}">
        {{item.content}}
      </view>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading" wx:if="{{isLoading}}">
      <text>AI正在思考中...</text>
    </view>
  </scroll-view>

  <!-- 建议回复 -->
  <view class="suggestions" wx:if="{{suggestions && suggestions.length > 0}}">
    <text class="suggestions-title">💡 建议回复：</text>
    <view class="suggestion-list">
      <button 
        class="suggestion-btn" 
        wx:for="{{suggestions}}" 
        wx:key="index"
        data-suggestion="{{item}}"
        bindtap="onSuggestionTap"
      >
        {{item}}
      </button>
    </view>
  </view>

  <!-- 输入区域 -->
  <view class="input-area">
    <input 
      class="input" 
      placeholder="输入你的消息..." 
      value="{{inputValue}}"
      bindinput="onInputChange"
      confirm-type="send"
      bindconfirm="sendMessage"
    />
    <button 
      class="send-btn {{inputValue ? 'active' : ''}}" 
      bindtap="sendMessage"
      disabled="{{isLoading}}"
    >
      发送
    </button>
  </view>
</view>
