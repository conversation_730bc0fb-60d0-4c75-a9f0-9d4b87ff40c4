/* pages/test/test.wxss */

.test-page {
  padding: 32rpx;
  background-color: #ffffff;
  min-height: 100vh;
}

.test-section {
  margin-bottom: 64rpx;
}

.test-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #0a0a0a;
  margin-bottom: 16rpx;
}

.test-subtitle {
  display: block;
  font-size: 28rpx;
  color: #737373;
  margin-bottom: 32rpx;
}

.section-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #0a0a0a;
  margin-bottom: 24rpx;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  align-items: flex-start;
}

.color-box {
  padding: 24rpx;
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  border: 1px solid #e5e5e5;
}

.color-box text {
  font-size: 28rpx;
  color: inherit;
}
