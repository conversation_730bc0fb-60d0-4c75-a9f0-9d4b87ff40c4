# 简历故事 - 设计系统

> 基于乔布斯和乔纳森的设计理念，以信息为核心，辅以讲故事的氛围

## 🎯 设计理念

### 核心原则
1. **极简主义** - 去除一切不必要的视觉元素
2. **以内容为王** - 让信息成为界面的主角
3. **故事化体验** - 通过设计传达叙事感
4. **系统化思维** - 建立统一的设计语言

### 设计哲学
- "简约就是终极的复杂" - 乔布斯
- "好的设计是显而易见的，伟大的设计是透明的" - 乔纳森·艾维
- 每个像素都有其存在的理由

## 🎨 视觉系统

### 颜色系统
```css
/* 主色调 - 基于语义化的单色调色板 */
--color-background: #ffffff;
--color-background-subtle: #fafafa;
--color-background-muted: #f5f5f5;

--color-foreground: #0a0a0a;
--color-foreground-muted: #737373;
--color-foreground-subtle: #a3a3a3;

--color-border: #e5e5e5;
--color-border-subtle: #f0f0f0;

--color-accent: #0a0a0a;
--color-accent-foreground: #ffffff;
```

**设计思考：**
- 采用近乎单色的调色板，避免色彩干扰内容
- 仅在必要时使用功能性颜色（成功、警告、错误）
- 通过明度变化建立层级关系

### 字体系统
```css
/* 清晰的信息层级 */
--font-size-xs: 24rpx;    /* 辅助信息 */
--font-size-sm: 28rpx;    /* 次要内容 */
--font-size-base: 32rpx;  /* 正文 */
--font-size-lg: 36rpx;    /* 小标题 */
--font-size-xl: 40rpx;    /* 标题 */
--font-size-2xl: 48rpx;   /* 大标题 */
--font-size-3xl: 60rpx;   /* 主标题 */
--font-size-4xl: 72rpx;   /* 品牌标题 */
```

**设计思考：**
- 基于8rpx的倍数关系，确保视觉和谐
- 字重仅使用 400、500、600、700 四个级别
- 行高采用 1.25、1.5、1.75 三个比例

### 间距系统
```css
/* 基于8rpx网格的间距系统 */
--spacing-1: 8rpx;    /* 最小间距 */
--spacing-2: 16rpx;   /* 小间距 */
--spacing-3: 24rpx;   /* 中间距 */
--spacing-4: 32rpx;   /* 标准间距 */
--spacing-6: 48rpx;   /* 大间距 */
--spacing-8: 64rpx;   /* 区块间距 */
--spacing-12: 96rpx;  /* 章节间距 */
--spacing-16: 128rpx; /* 页面间距 */
```

## 🧩 组件系统

### 按钮组件
- **Primary**: 主要操作，黑色背景
- **Secondary**: 次要操作，边框样式
- **Ghost**: 辅助操作，透明背景
- **Destructive**: 危险操作，红色背景

### 卡片组件
- 极简边框，微妙阴影
- 悬停时轻微提升
- 内容为主，装饰为辅

### 输入组件
- 清晰的焦点状态
- 一致的内边距
- 语义化的占位符

## 📱 界面设计

### 首页设计
**设计目标：** 营造专业、温暖的第一印象

**关键元素：**
- 极简的品牌展示
- 清晰的价值主张
- 专注的操作引导

**设计细节：**
- 大量留白营造呼吸感
- 渐进式信息披露
- 微妙的交互反馈

### 对话页面设计
**设计目标：** 创造自然、专注的对话体验

**关键元素：**
- 清晰的消息层级
- 温暖的对话氛围
- 智能的状态反馈

**设计细节：**
- 消息气泡采用不同的圆角处理
- 输入状态用动画表达
- 进度指示器简洁明了

## 🎭 动效系统

### 动画原则
- **有意义的动效** - 每个动画都有明确的功能目的
- **自然的缓动** - 使用物理感的缓动函数
- **适度的时长** - 快速响应，不拖沓

### 标准动效
```css
/* 标准缓动函数 */
--ease-out: cubic-bezier(0.16, 1, 0.3, 1);
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

/* 标准时长 */
--duration-fast: 150ms;    /* 快速反馈 */
--duration-normal: 250ms;  /* 标准过渡 */
--duration-slow: 350ms;    /* 复杂动画 */
```

## 📐 布局系统

### 网格系统
- 基于750rpx的设计宽度
- 最大内容宽度600rpx，确保阅读舒适度
- 响应式断点：600rpx

### 信息架构
1. **主要内容区** - 核心信息展示
2. **操作区** - 用户交互控件
3. **辅助信息区** - 次要信息和帮助

## 🔧 实现规范

### CSS 组织
- 使用CSS变量统一管理设计令牌
- 采用BEM命名规范
- 组件样式独立，避免全局污染

### 组件开发
- 每个组件都有明确的API
- 支持主题定制
- 保持向后兼容

### 质量标准
- 所有交互都有适当的反馈
- 支持无障碍访问
- 在不同设备上保持一致性

## 🚀 未来规划

### 短期目标
- [ ] 完善所有基础组件
- [ ] 建立组件文档
- [ ] 实现暗色主题

### 长期愿景
- 成为小程序设计系统的标杆
- 影响更多产品采用以内容为核心的设计理念
- 推动整个行业的设计品质提升

---

*"设计不仅仅是看起来如何，感觉如何。设计是如何工作的。" - 史蒂夫·乔布斯*
