<!--pages/index/index.wxml-->
<view class="page">
  <!-- 主要内容区域 -->
  <view class="main-content">

    <!-- Hero 区域 - 极简且有力 -->
    <view class="hero">
      <view class="hero-content">
        <view class="brand">
          <text class="brand-name">简历故事</text>
          <text class="brand-tagline">Resume Story</text>
        </view>

        <view class="hero-message">
          <text class="hero-title">每个人都有值得讲述的故事</text>
          <text class="hero-subtitle">通过对话，让AI帮你发现并讲述属于你的职业故事</text>
        </view>
      </view>
    </view>

    <!-- 核心价值主张 -->
    <view class="value-proposition">
      <view class="value-item">
        <view class="value-content">
          <text class="value-title">对话式体验</text>
          <text class="value-description">像和朋友聊天一样，自然地分享你的经历</text>
        </view>
      </view>

      <view class="value-item">
        <view class="value-content">
          <text class="value-title">智能信息提取</text>
          <text class="value-description">AI自动识别关键信息，构建完整的职业画像</text>
        </view>
      </view>

      <view class="value-item">
        <view class="value-content">
          <text class="value-title">专业简历生成</text>
          <text class="value-description">基于你的故事，生成符合行业标准的简历</text>
        </view>
      </view>
    </view>

  </view>

  <!-- 底部操作区域 -->
  <view class="action-area">
    <view wx:if="{{!hasUserInfo}}" class="auth-section">
      <view class="auth-content">
        <text class="auth-message">开始讲述你的故事</text>
        <ui-button
          variant="primary"
          size="default"
          text="开始制作简历"
          className="auth-button"
          bindtap="getUserProfile"
        ></ui-button>
        <text class="auth-note">需要基本信息来个性化你的简历</text>
      </view>
    </view>

    <view wx:else class="user-section">
      <view class="user-content">
        <text class="welcome-message">欢迎回来，{{userInfo.nickName}}</text>
        <view class="action-buttons">
          <ui-button
            variant="primary"
            size="default"
            text="继续我的故事"
            bindtap="startResumeChat"
          ></ui-button>
          <ui-button
            variant="ghost"
            size="default"
            text="查看示例"
            bindtap="viewSampleResume"
          ></ui-button>
        </view>
      </view>
    </view>
  </view>

  <!-- 辅助信息 -->
  <view class="footer-info">
    <view class="info-item" bindtap="viewHelp">
      <text class="info-text">使用指南</text>
    </view>
    <view class="info-item" bindtap="viewSampleResume">
      <text class="info-text">示例简历</text>
    </view>
  </view>
</view>

