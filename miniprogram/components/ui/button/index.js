// components/ui/button/index.js
Component({
  properties: {
    // 按钮变体
    variant: {
      type: String,
      value: 'primary' // primary, secondary, ghost, destructive
    },
    
    // 按钮尺寸
    size: {
      type: String,
      value: 'default' // sm, default, lg
    },
    
    // 按钮文本
    text: {
      type: String,
      value: ''
    },
    
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    
    // 是否加载中
    loading: {
      type: Boolean,
      value: false
    },
    
    // 自定义类名
    className: {
      type: String,
      value: ''
    }
  },

  methods: {
    onTap(e) {
      if (this.data.disabled || this.data.loading) {
        return;
      }
      
      this.triggerEvent('tap', e.detail);
    }
  }
});
