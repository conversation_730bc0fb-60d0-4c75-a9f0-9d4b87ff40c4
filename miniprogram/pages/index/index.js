// pages/index/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: null,
    hasUserInfo: false,

    // 产品介绍
    features: [
      {
        icon: '💬',
        title: '对话式制作',
        desc: '像聊天一样轻松制作简历'
      },
      {
        icon: '🤖',
        title: 'AI智能优化',
        desc: '专业AI帮你发现亮点'
      },
      {
        icon: '🎯',
        title: '职位匹配',
        desc: '针对性优化提升成功率'
      },
      {
        icon: '📄',
        title: '多种模板',
        desc: '精美模板一键生成'
      }
    ],

    // 统计数据
    stats: {
      users: '10,000+',
      resumes: '50,000+',
      success: '95%'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.checkUserInfo()
  },

  /**
   * 检查用户信息
   */
  checkUserInfo() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      })
    }
  },

  /**
   * 获取用户信息
   */
  getUserProfile() {
    wx.getUserProfile({
      desc: '用于完善简历信息',
      success: (res) => {
        const userInfo = res.userInfo
        this.setData({
          userInfo: userInfo,
          hasUserInfo: true
        })

        // 保存用户信息
        wx.setStorageSync('userInfo', userInfo)

        // 跳转到对话页面
        this.startResumeChat()
      },
      fail: () => {
        wx.showToast({
          title: '需要授权才能使用',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 跳转到个人信息页面
   */
  goToProfile() {
    wx.navigateTo({
      url: '/pages/profile/profile'
    })
  },

  /**
   * 开始简历对话
   */
  startResumeChat() {
    wx.navigateTo({
      url: '/pages/chat/chat'
    })
  },

  /**
   * 查看示例简历
   */
  viewSampleResume() {
    wx.navigateTo({
      url: '/pages/preview/preview?sample=true'
    })
  },

  /**
   * 查看帮助
   */
  viewHelp() {
    wx.showModal({
      title: '如何使用',
      content: '1. 点击"开始制作"按钮\n2. 与AI助手对话，分享你的经历\n3. AI会自动整理信息\n4. 选择模板生成精美简历\n5. 导出或分享你的简历',
      showCancel: false,
      confirmText: '我知道了'
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '简历故事 - AI帮你写出精彩简历',
      path: '/pages/index/index',
      imageUrl: '/imgs/share-cover.png'
    }
  }
})
