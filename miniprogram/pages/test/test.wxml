<!--pages/test/test.wxml-->
<view class="test-page">
  <view class="test-section">
    <text class="test-title">设计系统测试页面</text>
    <text class="test-subtitle">验证样式是否正确加载</text>
  </view>

  <view class="test-section">
    <text class="section-title">按钮测试</text>
    <view class="button-group">
      <ui-button variant="primary" size="default" text="主要按钮"></ui-button>
      <ui-button variant="secondary" size="default" text="次要按钮"></ui-button>
      <ui-button variant="ghost" size="default" text="幽灵按钮"></ui-button>
    </view>
  </view>

  <view class="test-section">
    <text class="section-title">字体测试</text>
    <text class="text-4xl">超大标题 (72rpx)</text>
    <text class="text-3xl">大标题 (60rpx)</text>
    <text class="text-2xl">标题 (48rpx)</text>
    <text class="text-xl">小标题 (40rpx)</text>
    <text class="text-lg">大文本 (36rpx)</text>
    <text class="text-base">正文 (32rpx)</text>
    <text class="text-sm">小文本 (28rpx)</text>
    <text class="text-xs">极小文本 (24rpx)</text>
  </view>

  <view class="test-section">
    <text class="section-title">颜色测试</text>
    <view class="color-box" style="background-color: #ffffff; border: 1px solid #e5e5e5;">
      <text>背景色 #ffffff</text>
    </view>
    <view class="color-box" style="background-color: #fafafa;">
      <text>浅背景 #fafafa</text>
    </view>
    <view class="color-box" style="background-color: #f5f5f5;">
      <text>静音背景 #f5f5f5</text>
    </view>
    <view class="color-box" style="background-color: #0a0a0a; color: #ffffff;">
      <text>强调色 #0a0a0a</text>
    </view>
  </view>

  <view class="test-section">
    <text class="section-title">卡片测试</text>
    <view class="card">
      <view class="card-header">
        <text class="card-title">卡片标题</text>
      </view>
      <text class="card-description">这是一个测试卡片，用来验证卡片样式是否正确显示。</text>
    </view>
  </view>
</view>
