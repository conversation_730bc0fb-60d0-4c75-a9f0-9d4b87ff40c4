/* pages/index/index.wxss */

/**
 * 首页设计 - 基于乔布斯和乔纳森的设计理念
 * 极简、专注、以内容为核心
 */

/* ========================================
   页面布局
   ======================================== */

.page {
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 64rpx 32rpx;
  max-width: 750rpx;
  margin: 0 auto;
  width: 100%;
}

/* ========================================
   Hero 区域 - 极简且有力
   ======================================== */

.hero {
  text-align: center;
  margin-bottom: 160rpx;
}

.hero-content {
  max-width: 600rpx;
  margin: 0 auto;
}

.brand {
  margin-bottom: 128rpx;
}

.brand-name {
  display: block;
  font-size: 72rpx;
  font-weight: 700;
  color: #0a0a0a;
  margin-bottom: 16rpx;
  letter-spacing: -0.02em;
}

.brand-tagline {
  display: block;
  font-size: 28rpx;
  color: #a3a3a3;
  font-weight: 500;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}

.hero-message {
  margin-bottom: 96rpx;
}

.hero-title {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #0a0a0a;
  line-height: 1.25;
  margin-bottom: 32rpx;
}

.hero-subtitle {
  display: block;
  font-size: 32rpx;
  color: #737373;
  line-height: 1.75;
  max-width: 480rpx;
  margin: 0 auto;
}

/* ========================================
   核心价值主张 - 清晰的信息层级
   ======================================== */

.value-proposition {
  margin-bottom: 160rpx;
}

.value-item {
  padding: 64rpx 0;
  border-bottom: 1px solid #f0f0f0;
  transition: all 250ms cubic-bezier(0.16, 1, 0.3, 1);
}

.value-item:last-child {
  border-bottom: none;
}

.value-item:active {
  background-color: #fafafa;
  margin: 0 -32rpx;
  padding-left: 32rpx;
  padding-right: 32rpx;
  border-radius: 12rpx;
}

.value-content {
  max-width: 480rpx;
  margin: 0 auto;
  text-align: center;
}

.value-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #0a0a0a;
  margin-bottom: 16rpx;
}

.value-description {
  display: block;
  font-size: 28rpx;
  color: #737373;
  line-height: 1.75;
}

/* ========================================
   操作区域 - 专注且清晰
   ======================================== */

.action-area {
  padding: 64rpx 32rpx;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.auth-section,
.user-section {
  max-width: 480rpx;
  margin: 0 auto;
  text-align: center;
}

.auth-content,
.user-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
}

.auth-message,
.welcome-message {
  font-size: 36rpx;
  font-weight: 500;
  color: #0a0a0a;
}

.auth-button {
  margin: 16rpx 0;
}

.auth-note {
  font-size: 24rpx;
  color: #a3a3a3;
  line-height: 1.75;
}

.action-buttons {
  display: flex;
  gap: 24rpx;
  margin-top: 16rpx;
}

/* ========================================
   辅助信息 - 低调且有用
   ======================================== */

.footer-info {
  padding: 48rpx 32rpx;
  display: flex;
  justify-content: center;
  gap: 64rpx;
  border-top: 1px solid #f0f0f0;
}

.info-item {
  transition: all 150ms cubic-bezier(0.16, 1, 0.3, 1);
}

.info-item:active {
  transform: translateY(-2rpx);
}

.info-text {
  font-size: 28rpx;
  color: #a3a3a3;
  font-weight: 500;
}

.info-item:active .info-text {
  color: #737373;
}


/* ========================================
   响应式设计
   ======================================== */

@media (max-width: 600rpx) {
  .hero-title {
    font-size: 40rpx;
  }

  .brand-name {
    font-size: 60rpx;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .action-buttons .btn {
    width: 100%;
  }

  .footer-info {
    gap: 32rpx;
  }
}