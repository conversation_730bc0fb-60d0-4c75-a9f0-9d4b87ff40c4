// cloudfunctions/conversationManage/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 对话管理云函数
 * 支持创建、获取、更新对话记录
 */
exports.main = async (event, context) => {
  const { action, conversationId, stage, messages, resumeData } = event
  const { OPENID } = cloud.getWXContext()

  try {
    switch (action) {
      case 'create':
        return await createConversation(OPENID, stage)
      case 'get':
        return await getConversation(conversationId, OPENID)
      case 'update':
        return await updateConversation(conversationId, OPENID, { stage, messages, resumeData })
      case 'list':
        return await listConversations(OPENID)
      default:
        return {
          success: false,
          error: '不支持的操作类型'
        }
    }
  } catch (error) {
    console.error('对话管理失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 创建新对话
 */
async function createConversation(userId, stage = 'greeting') {
  const conversation = {
    userId: userId,
    stage: stage,
    messages: [],
    resumeData: {
      personalInfo: {},
      jobIntention: {},
      education: [],
      workExperience: [],
      projects: [],
      skills: [],
      personalTraits: [],
      completeness: 0
    },
    createdAt: new Date(),
    updatedAt: new Date()
  }

  await db.collection('conversations').add({
    data: conversation
  })

  return {
    success: true,
    conversationId: conversationId,
    conversation: conversation
  }
}

/**
 * 获取对话记录
 */
async function getConversation(conversationId, userId) {
  const result = await db.collection('conversations')
    .where({
      _id: conversationId,
      userId: userId
    })
    .get()

  if (result.data.length === 0) {
    return {
      success: false,
      error: '对话记录不存在'
    }
  }

  return {
    success: true,
    conversation: result.data[0]
  }
}

/**
 * 更新对话记录
 */
async function updateConversation(conversationId, userId, updateData) {
  const result = await db.collection('conversations')
    .where({
      _id: conversationId,
      userId: userId
    })
    .update({
      data: {
        ...updateData,
        updatedAt: new Date()
      }
    })

  return {
    success: true,
    updated: result.stats.updated
  }
}

/**
 * 获取用户的对话列表
 */
async function listConversations(userId, limit = 20) {
  const result = await db.collection('conversations')
    .where({
      userId: userId
    })
    .orderBy('updatedAt', 'desc')
    .limit(limit)
    .get()

  return {
    success: true,
    conversations: result.data
  }
}
