/**
 * 简历故事 - 设计系统
 * 基于乔布斯和乔纳森的设计理念
 * 以信息为核心，辅以讲故事的氛围
 *
 * 设计令牌 (Design Tokens)
 * 颜色系统: #ffffff, #fafafa, #f5f5f5, #0a0a0a, #737373, #a3a3a3
 * 字体系统: 24rpx, 28rpx, 32rpx, 36rpx, 40rpx, 48rpx, 60rpx, 72rpx
 * 间距系统: 8rpx, 16rpx, 24rpx, 32rpx, 48rpx, 64rpx, 96rpx, 128rpx
 */

/* ========================================
   基础重置
   ======================================== */

page {
  background-color: #ffffff;
  color: #0a0a0a;
  font-size: 32rpx;
  line-height: 1.5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
}

/* 移除所有默认样式 */
view, text, button, input, textarea, scroll-view {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* ========================================
   基础组件
   ======================================== */

/* 按钮系统 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 150ms ease-out;
  border: none;
  outline: none;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

.btn-primary {
  background-color: #0a0a0a;
  color: #ffffff;
  padding: 24rpx 48rpx;
  min-height: 88rpx;
}

.btn-primary:active {
  background-color: #737373;
  transform: translateY(1rpx);
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #0a0a0a;
  padding: 24rpx 48rpx;
  min-height: 88rpx;
  border: 1px solid #e5e5e5;
}

.btn-secondary:active {
  background-color: #fafafa;
  border-color: #e5e5e5;
}

.btn-ghost {
  background-color: transparent;
  color: #737373;
  padding: 16rpx 32rpx;
}

.btn-ghost:active {
  background-color: #fafafa;
  color: #0a0a0a;
}

/* 卡片系统 */
.card {
  background-color: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 16rpx;
  padding: 48rpx;
  transition: all 250ms ease-out;
}

.card:active {
  border-color: #e5e5e5;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.card-header {
  margin-bottom: 32rpx;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #0a0a0a;
  margin-bottom: 8rpx;
}

.card-description {
  font-size: 28rpx;
  color: #737373;
  line-height: 1.75;
}

/* 输入框系统 - 优雅且专注 */
.input {
  width: 100%;
  padding: 20rpx 24rpx;
  border: 1px solid #f0f0f0;
  border-radius: 12rpx;
  font-size: 30rpx;
  background-color: #fafafa;
  color: #0a0a0a;
  transition: all 150ms ease-out;
  font-weight: 400;
  line-height: 1.5;
}

.input:focus {
  outline: none;
  background-color: #ffffff;
  border-color: #e5e5e5;
  box-shadow: 0 0 0 3px rgba(10, 10, 10, 0.05);
}

.input::placeholder {
  color: #a3a3a3;
  font-weight: 400;
}

/* ========================================
   布局系统
   ======================================== */

.container {
  max-width: 750rpx;
  margin: 0 auto;
  padding: 0 32rpx;
}

.section {
  padding: 128rpx 0;
}

.section-sm {
  padding: 96rpx 0;
}

.section-lg {
  padding: 192rpx 0;
}

/* Flexbox 工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 { gap: 8rpx; }
.gap-2 { gap: 16rpx; }
.gap-3 { gap: 24rpx; }
.gap-4 { gap: 32rpx; }
.gap-6 { gap: 48rpx; }
.gap-8 { gap: 64rpx; }

/* ========================================
   字体系统
   ======================================== */

.text-xs { font-size: 24rpx; }
.text-sm { font-size: 28rpx; }
.text-base { font-size: 32rpx; }
.text-lg { font-size: 36rpx; }
.text-xl { font-size: 40rpx; }
.text-2xl { font-size: 48rpx; }
.text-3xl { font-size: 60rpx; }
.text-4xl { font-size: 72rpx; }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.leading-tight { line-height: 1.25; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.75; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* 颜色工具类 */
.text-foreground { color: #0a0a0a; }
.text-muted { color: #737373; }
.text-subtle { color: #a3a3a3; }
.text-primary { color: #2563eb; }

/* ========================================
   间距系统
   ======================================== */

.p-1 { padding: 8rpx; }
.p-2 { padding: 16rpx; }
.p-3 { padding: 24rpx; }
.p-4 { padding: 32rpx; }
.p-6 { padding: 48rpx; }
.p-8 { padding: 64rpx; }

.px-1 { padding-left: 8rpx; padding-right: 8rpx; }
.px-2 { padding-left: 16rpx; padding-right: 16rpx; }
.px-4 { padding-left: 32rpx; padding-right: 32rpx; }
.px-6 { padding-left: 48rpx; padding-right: 48rpx; }

.py-1 { padding-top: 8rpx; padding-bottom: 8rpx; }
.py-2 { padding-top: 16rpx; padding-bottom: 16rpx; }
.py-4 { padding-top: 32rpx; padding-bottom: 32rpx; }
.py-6 { padding-top: 48rpx; padding-bottom: 48rpx; }

.m-1 { margin: 8rpx; }
.m-2 { margin: 16rpx; }
.m-4 { margin: 32rpx; }
.m-6 { margin: 48rpx; }

.mb-1 { margin-bottom: 8rpx; }
.mb-2 { margin-bottom: 16rpx; }
.mb-4 { margin-bottom: 32rpx; }
.mb-6 { margin-bottom: 48rpx; }
.mb-8 { margin-bottom: 64rpx; }

.mt-1 { margin-top: 8rpx; }
.mt-2 { margin-top: 16rpx; }
.mt-4 { margin-top: 32rpx; }
.mt-6 { margin-top: 48rpx; }
.mt-8 { margin-top: 64rpx; }

/* ========================================
   动画系统
   ======================================== */

.animate-fade-in {
  animation: fadeIn 250ms cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-slide-up {
  animation: slideUp 250ms cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-scale-in {
  animation: scaleIn 150ms cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ========================================
   状态系统
   ======================================== */

.loading {
  opacity: 0.6;
  pointer-events: none;
}

.disabled {
  opacity: 0.4;
  pointer-events: none;
}

.hidden {
  display: none;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}