/* pages/chat/chat.wxss */

/**
 * 对话页面设计 - 专注于对话体验
 * 营造温暖、专业的对话氛围
 */

/* ========================================
   页面布局
   ======================================== */

.chat-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

/* ========================================
   极简顶部栏 - 减少视觉干扰
   ======================================== */

.minimal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 10;
  min-height: 88rpx;
}

.conversation-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.current-stage {
  font-size: 28rpx;
  font-weight: 500;
  color: #0a0a0a;
}

.progress-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #e5e5e5;
  transition: all 250ms ease-out;
}

.progress-dot.active {
  background-color: #0a0a0a;
  transform: scale(1.2);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.progress-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #a3a3a3;
  min-width: 60rpx;
  text-align: right;
}

/* ========================================
   对话区域
   ======================================== */

.conversation-area {
  flex: 1;
  padding: 0 32rpx;
  overflow-y: auto;
}

.conversation-content {
  max-width: 600rpx;
  margin: 0 auto;
  padding: 32rpx 0 64rpx;
}

/* 欢迎区域 - 更加内敛 */
.welcome-section {
  text-align: center;
  margin-bottom: 64rpx;
  padding: 32rpx 0;
}

.welcome-message {
  max-width: 400rpx;
  margin: 0 auto;
}

.welcome-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #0a0a0a;
  margin-bottom: 12rpx;
  line-height: 1.3;
}

.welcome-subtitle {
  display: block;
  font-size: 26rpx;
  color: #737373;
  line-height: 1.6;
  font-weight: 400;
}

/* 消息列表 */
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 48rpx;
}

.message-group {
  animation: slideUp 250ms cubic-bezier(0.16, 1, 0.3, 1);
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
}

.ai-message {
  align-self: flex-start;
}

.user-message {
  align-self: flex-end;
}

.message-content {
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 18rpx;
  padding: 24rpx 28rpx;
  position: relative;
  transition: all 150ms ease-out;
}

.ai-message .message-content {
  background-color: #fafafa;
  border-color: #f0f0f0;
  border-bottom-left-radius: 8rpx;
}

.user-message .message-content {
  background-color: #0a0a0a;
  border-color: #0a0a0a;
  color: #ffffff;
  border-bottom-right-radius: 8rpx;
}

.message-text {
  display: block;
  font-size: 30rpx;
  line-height: 1.6;
  margin-bottom: 12rpx;
  font-weight: 400;
}

.message-time {
  display: block;
  font-size: 22rpx;
  opacity: 0.5;
  font-weight: 500;
}

.user-message .message-time {
  color: #ffffff;
}

/* 输入状态指示器 */
.typing-message .message-content {
  padding: 32rpx;
}

.typing-indicator {
  display: flex;
  gap: 8rpx;
  align-items: center;
}

.typing-dot {
  width: 8rpx;
  height: 8rpx;
  background-color: #a3a3a3;
  border-radius: 50%;
  animation: typingPulse 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingPulse {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  30% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* ========================================
   快捷回复区域
   ======================================== */

.quick-replies-area {
  padding: 20rpx 32rpx 24rpx;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
}

.quick-replies-header {
  margin-bottom: 16rpx;
}

.quick-replies-title {
  font-size: 24rpx;
  color: #a3a3a3;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.quick-replies-scroll {
  white-space: nowrap;
}

.quick-replies-list {
  display: inline-flex;
  gap: 12rpx;
  padding-bottom: 8rpx;
}

.quick-reply-chip {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 20rpx;
  transition: all 150ms ease-out;
  white-space: nowrap;
}

.quick-reply-chip:active {
  background-color: #f5f5f5;
  border-color: #e5e5e5;
  transform: scale(0.96);
}

.quick-reply-text {
  font-size: 26rpx;
  color: #0a0a0a;
  font-weight: 400;
}

/* ========================================
   简历完整度指示器
   ======================================== */

.completeness-indicator {
  padding: 20rpx 32rpx 24rpx;
  background-color: #fafafa;
  border-top: 1px solid #f0f0f0;
}

.completeness-content {
  max-width: 600rpx;
  margin: 0 auto;
}

.completeness-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.completeness-label {
  font-size: 24rpx;
  color: #a3a3a3;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.completeness-percentage {
  font-size: 24rpx;
  color: #737373;
  font-weight: 600;
}

.completeness-bar {
  height: 4rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.completeness-progress {
  height: 100%;
  background-color: #737373;
  border-radius: 4rpx;
  transition: width 350ms ease-out;
}

/* ========================================
   优雅输入区域 - 专注于内容创作
   ======================================== */

.compose-area {
  padding: 24rpx 32rpx 32rpx;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
  position: sticky;
  bottom: 0;
}

.compose-container {
  max-width: 600rpx;
  margin: 0 auto;
}

.input-wrapper {
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  transition: all 150ms ease-out;
}

.input-wrapper:focus-within {
  background-color: #ffffff;
  border-color: #e5e5e5;
  box-shadow: 0 0 0 3px rgba(10, 10, 10, 0.05);
}

.compose-input {
  width: 100%;
  min-height: 48rpx;
  max-height: 200rpx;
  font-size: 32rpx;
  line-height: 1.5;
  color: #0a0a0a;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
}

.compose-input::placeholder {
  color: #a3a3a3;
  font-weight: 400;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1px solid #f0f0f0;
}

.char-count {
  font-size: 24rpx;
  color: #a3a3a3;
  font-weight: 500;
}

.char-count.warning {
  color: #d97706;
}

.send-action {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 150ms ease-out;
  cursor: pointer;
}

.send-action.inactive {
  background-color: #f5f5f5;
  border: 1px solid #e5e5e5;
}

.send-action.active {
  background-color: #0a0a0a;
  border: 1px solid #0a0a0a;
  transform: scale(1.05);
}

.send-action:active {
  transform: scale(0.95);
}

.send-icon {
  font-size: 32rpx;
  font-weight: 600;
  color: #a3a3a3;
  line-height: 1;
}

.send-action.active .send-icon {
  color: #ffffff;
}

/* ========================================
   响应式设计
   ======================================== */

/* ========================================
   响应式设计
   ======================================== */

@media (max-width: 600rpx) {
  .status-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }

  .progress-indicator {
    align-self: flex-end;
  }

  .conversation-content {
    padding: 32rpx 0;
  }

  .message {
    max-width: 90%;
  }

  .welcome-title {
    font-size: 36rpx;
  }
}
