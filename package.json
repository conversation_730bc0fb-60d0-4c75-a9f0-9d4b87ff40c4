{"name": "ai-resume-assistant", "version": "1.0.0", "description": "AI简历顾问小程序 - 集成腾讯云开发AI能力", "scripts": {"deploy": "./deploy-ai.sh", "deploy:full": "./scripts/deploy.sh", "deploy:aiChat": "/Applications/wechatwebdevtools.app/Contents/MacOS/cli cloud functions deploy --env aicv-3g1rr6glfc42a1eb --names aiChat --project /Users/<USER>/WeChatProjects/miniprogram-3 --remote-npm-install", "deploy:conversation": "/Applications/wechatwebdevtools.app/Contents/MacOS/cli cloud functions deploy --env aicv-3g1rr6glfc42a1eb --names conversationManage --project /Users/<USER>/WeChatProjects/miniprogram-3 --remote-npm-install", "deploy:userProfile": "/Applications/wechatwebdevtools.app/Contents/MacOS/cli cloud functions deploy --env aicv-3g1rr6glfc42a1eb --names userProfile --project /Users/<USER>/WeChatProjects/miniprogram-3 --remote-npm-install", "preview": "/Applications/wechatwebdevtools.app/Contents/MacOS/cli preview --project /Users/<USER>/WeChatProjects/miniprogram-3", "open": "/Applications/wechatwebdevtools.app/Contents/MacOS/cli open --project /Users/<USER>/WeChatProjects/miniprogram-3", "login": "/Applications/wechatwebdevtools.app/Contents/MacOS/cli login --project /Users/<USER>/WeChatProjects/miniprogram-3", "status": "/Applications/wechatwebdevtools.app/Contents/MacOS/cli cloud functions list --env aicv-3g1rr6glfc42a1eb --project /Users/<USER>/WeChatProjects/miniprogram-3"}, "keywords": ["miniprogram", "wechat", "ai", "resume", "cloudbase"], "author": "Resume Story Team", "license": "MIT", "devDependencies": {}, "repository": {"type": "git", "url": "https://github.com/your-username/ai-resume-assistant.git"}}